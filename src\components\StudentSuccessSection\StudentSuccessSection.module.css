/* Student Success Section */
.studentSuccessSection {
  position: relative;
  padding: 120px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  overflow: hidden;
  min-height: 100vh;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floatingShape1 {
  position: absolute;
  top: 10%;
  left: 5%;
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
  filter: blur(40px);
}

.floatingShape2 {
  position: absolute;
  bottom: 15%;
  right: 8%;
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, rgba(236, 72, 153, 0.1), rgba(251, 146, 60, 0.1));
  border-radius: 50%;
  animation: float 8s ease-in-out infinite reverse;
  filter: blur(30px);
}

.gradientOrb1 {
  position: absolute;
  top: 30%;
  right: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 4s ease-in-out infinite;
}

.gradientOrb2 {
  position: absolute;
  bottom: 20%;
  left: 15%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(147, 51, 234, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite reverse;
}

/* Container */
.container {
  position: relative;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  z-index: 1;
}

/* Header Section */
.headerSection {
  text-align: center;
  margin-bottom: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.headerSection.fadeInUp {
  opacity: 1;
  transform: translateY(0);
}

.badge {
  display: inline-block;
  padding: 12px 24px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 50px;
  color: #3b82f6;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
}

.mainTitle {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 24px;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.description {
  font-size: 1.25rem;
  color: #94a3b8;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Testimonials Container */
.testimonialsContainer {
  margin-bottom: 80px;
}

.testimonialsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

/* Testimonial Card */
.testimonialCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 32px;
  backdrop-filter: blur(20px);
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(30px);
  position: relative;
  overflow: hidden;
}

.testimonialCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonialCard.active::before,
.testimonialCard:hover::before {
  opacity: 1;
}

.testimonialCard.slideInUp {
  opacity: 1;
  transform: translateY(0);
}

.testimonialCard.active,
.testimonialCard:hover {
  transform: translateY(-8px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Card Header */
.cardHeader {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
}

.studentImage {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.testimonialCard:hover .studentImage {
  border-color: #3b82f6;
  transform: scale(1.05);
}

.studentImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonialCard:hover .imageOverlay {
  opacity: 1;
}

.studentInfo {
  flex: 1;
}

.studentName {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
}

.studentRole {
  font-size: 0.9rem;
  color: #3b82f6;
  margin-bottom: 4px;
  font-weight: 500;
}

.university {
  font-size: 0.85rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Rating */
.rating {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
}

.starFilled {
  color: #fbbf24;
  font-size: 1rem;
}

.starEmpty {
  color: #374151;
  font-size: 1rem;
}

/* Quote */
.quote {
  position: relative;
  margin-bottom: 24px;
}

.quote .fa-quote-left {
  position: absolute;
  top: -10px;
  left: -10px;
  font-size: 1.5rem;
  color: rgba(59, 130, 246, 0.3);
}

.quote .fa-quote-right {
  position: absolute;
  bottom: -10px;
  right: -10px;
  font-size: 1.5rem;
  color: rgba(59, 130, 246, 0.3);
}

.quote p {
  font-size: 1rem;
  line-height: 1.7;
  color: #e2e8f0;
  font-style: italic;
  padding: 0 20px;
  margin: 0;
}

/* Card Footer */
.cardFooter {
  display: flex;
  justify-content: flex-end;
}

.verifiedBadge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  color: #22c55e;
  font-weight: 500;
}

.verifiedBadge i {
  font-size: 0.9rem;
}

/* Navigation Dots */
.navigationDots {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot:hover,
.dot.activeDot {
  background: #3b82f6;
  transform: scale(1.2);
}

/* CTA Section */
.ctaSection {
  text-align: center;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.ctaSection.fadeInUp {
  opacity: 1;
  transform: translateY(0);
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 16px;
  line-height: 1.2;
}

.ctaDescription {
  font-size: 1.1rem;
  color: #94a3b8;
  margin-bottom: 32px;
  line-height: 1.6;
}

.ctaButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 36px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  border-radius: 50px;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
}

.ctaButton i {
  transition: transform 0.3s ease;
}

.ctaButton:hover i {
  transform: translateX(4px);
}

.buttonRipple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.ctaButton:hover .buttonRipple {
  transform: translateX(100%);
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .studentSuccessSection {
    padding: 80px 0;
  }

  .mainTitle {
    font-size: 3rem;
  }

  .testimonialsGrid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
  }

  .testimonialCard {
    padding: 24px;
  }

  .ctaTitle {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .studentSuccessSection {
    padding: 60px 0;
  }

  .container {
    padding: 0 16px;
  }

  .headerSection {
    margin-bottom: 60px;
  }

  .mainTitle {
    font-size: 2.5rem;
  }

  .description {
    font-size: 1.1rem;
  }

  .testimonialsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .testimonialCard {
    padding: 20px;
  }

  .cardHeader {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .studentImage {
    width: 70px;
    height: 70px;
  }

  .quote p {
    font-size: 0.95rem;
    padding: 0 16px;
  }

  .ctaTitle {
    font-size: 1.8rem;
  }

  .ctaDescription {
    font-size: 1rem;
  }

  .ctaButton {
    padding: 16px 28px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .studentSuccessSection {
    padding: 40px 0;
  }

  .mainTitle {
    font-size: 2rem;
  }

  .badge {
    padding: 10px 20px;
    font-size: 0.8rem;
  }

  .testimonialCard {
    padding: 16px;
  }

  .studentImage {
    width: 60px;
    height: 60px;
  }

  .studentName {
    font-size: 1.1rem;
  }

  .quote p {
    font-size: 0.9rem;
    padding: 0 12px;
  }

  .ctaTitle {
    font-size: 1.5rem;
  }

  .ctaButton {
    padding: 14px 24px;
    font-size: 0.95rem;
  }

  .floatingShape1,
  .floatingShape2,
  .gradientOrb1,
  .gradientOrb2 {
    display: none;
  }
}
