/* Services Hero Component Styles */

.servicesHero {
  height: 100vh;
  padding-top: 0; /* Removed padding to move content upwards */
  margin-top: -10px; /* Move content upwards by 10px */
  padding-bottom: 0;
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 25%, #2a3441 50%, #1e2a3a 75%, #0f1419 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

/* Enhanced Background Effects */
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

/* Animated Particle System */
.particleSystem {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: particleFloat 12s ease-in-out infinite var(--delay);
}

.particle:nth-child(1) { top: 10%; left: 5%; }
.particle:nth-child(2) { top: 20%; left: 15%; }
.particle:nth-child(3) { top: 30%; left: 25%; }
.particle:nth-child(4) { top: 40%; left: 35%; }
.particle:nth-child(5) { top: 50%; left: 45%; }
.particle:nth-child(6) { top: 60%; left: 55%; }
.particle:nth-child(7) { top: 70%; left: 65%; }
.particle:nth-child(8) { top: 80%; left: 75%; }
.particle:nth-child(9) { top: 15%; right: 10%; }
.particle:nth-child(10) { top: 25%; right: 20%; }
.particle:nth-child(11) { top: 35%; right: 30%; }
.particle:nth-child(12) { top: 45%; right: 40%; }
.particle:nth-child(13) { top: 55%; right: 50%; }
.particle:nth-child(14) { top: 65%; right: 60%; }
.particle:nth-child(15) { top: 75%; right: 70%; }
.particle:nth-child(16) { top: 85%; right: 80%; }
.particle:nth-child(17) { top: 12%; left: 50%; }
.particle:nth-child(18) { top: 32%; right: 15%; }
.particle:nth-child(19) { top: 52%; left: 80%; }
.particle:nth-child(20) { top: 72%; right: 25%; }

/* Geometric Shapes */
.geometricShapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.shape {
  position: absolute;
  border: 2px solid rgba(59, 130, 246, 0.2);
  animation: shapeFloat 15s ease-in-out infinite;
}

.shape:nth-child(1) { 
  width: 100px; height: 100px; top: 8%; left: 8%; 
  border-radius: 50%; animation-delay: 0s; 
}
.shape:nth-child(2) { 
  width: 80px; height: 80px; top: 15%; right: 12%; 
  transform: rotate(45deg); animation-delay: -2s; 
}
.shape:nth-child(3) { 
  width: 120px; height: 120px; bottom: 20%; left: 10%; 
  border-radius: 25px; animation-delay: -4s; 
}
.shape:nth-child(4) { 
  width: 90px; height: 90px; bottom: 30%; right: 18%; 
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%); animation-delay: -6s; 
}
.shape:nth-child(5) { 
  width: 110px; height: 110px; top: 50%; left: 5%; 
  border-radius: 50% 0; animation-delay: -8s; 
}
.shape:nth-child(6) { 
  width: 70px; height: 70px; top: 60%; right: 8%; 
  border-radius: 0 50% 0 50%; animation-delay: -10s; 
}
.shape:nth-child(7) { 
  width: 95px; height: 95px; top: 25%; left: 70%; 
  border-radius: 20px; animation-delay: -12s; 
}
.shape:nth-child(8) { 
  width: 85px; height: 85px; bottom: 15%; left: 60%; 
  border-radius: 50%; animation-delay: -14s; 
}

/* Gradient Waves */
.gradientWaves {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.wave1,
.wave2,
.wave3 {
  position: absolute;
  width: 300%;
  height: 300%;
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.08) 0%, transparent 70%);
  animation: waveMove 25s ease-in-out infinite;
}

.wave1 {
  top: -100%;
  left: -100%;
  animation-delay: 0s;
}

.wave2 {
  top: -50%;
  right: -100%;
  animation-delay: -8s;
  background: radial-gradient(ellipse at center, rgba(37, 99, 235, 0.06) 0%, transparent 70%);
}

.wave3 {
  bottom: -100%;
  left: -50%;
  animation-delay: -16s;
  background: radial-gradient(ellipse at center, rgba(29, 78, 216, 0.04) 0%, transparent 70%);
}

/* Floating Icons */
.floatingIcons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.floatingIcon {
  position: absolute;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(59, 130, 246, 0.6);
  font-size: 1.2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  animation: iconFloat 8s ease-in-out infinite;
}

.floatingIcon:nth-child(1) { top: 20%; left: 85%; animation-delay: 0s; }
.floatingIcon:nth-child(2) { top: 70%; left: 90%; animation-delay: -2s; }
.floatingIcon:nth-child(3) { top: 40%; left: 5%; animation-delay: -4s; }
.floatingIcon:nth-child(4) { top: 80%; left: 15%; animation-delay: -6s; }

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Two Column Grid Layout */
.heroGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  width: 100%;
  max-width: 1200px;
}

/* Left Column - Text Content */
.leftColumn {
  animation: fadeInLeft 1s ease-out;
  padding-right: 20px;
}

.heroText {
  max-width: 100%;
}

/* Right Column - Cards */
.rightColumn {
  animation: fadeInRight 1s ease-out 0.3s both;
  padding-left: 20px;
}

.headerBadge {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  color: #3b82f6;
  padding: 14px 32px;
  border-radius: 35px;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.headerBadge:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.headerBadge i {
  font-size: 1.1rem;
  color: #60a5fa;
}

.heroTitle {
  font-size: 4.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 20%, #ffffff 40%, #f1f5f9 60%, #ffffff 80%, #e2e8f0 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 30px;
  line-height: 1.1;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  animation: titleGlow 3s ease-in-out infinite;
  text-align: left;
}

.titleHighlight {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 30%, #2563eb 60%, #1d4ed8 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.titleHighlight::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  border-radius: 2px;
  animation: underlineGlow 2s ease-in-out infinite;
}

.heroDescription {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  margin-bottom: 40px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-align: left;
}

.heroActions {
  display: flex;
  gap: 20px;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}

.primaryButton {
  display: inline-flex;
  align-items: center;
  gap: 15px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  padding: 20px 40px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 15px 35px rgba(59, 130, 246, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.primaryButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.primaryButton:hover::before {
  left: 100%;
}

.primaryButton:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 25px 50px rgba(59, 130, 246, 0.5),
    0 10px 25px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
}

.primaryButton i {
  transition: transform 0.3s ease;
}

.primaryButton:hover i {
  transform: translateX(5px);
}

.secondaryButton {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  color: white;
  padding: 20px 35px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.secondaryButton:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.secondaryButton i {
  color: #60a5fa;
}

/* Key Highlights Section */
.highlightsSection {
  margin-bottom: 40px;
}

.highlightsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.highlightCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
  border-radius: 18px;
  padding: 25px 20px;
  text-align: center;
  backdrop-filter: blur(30px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation: cardSlideIn 0.8s ease-out var(--delay) both;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.2),
    0 5px 15px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.highlightCard:hover {
  transform: translateY(-8px) scale(1.03);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow:
    0 25px 60px rgba(0, 0, 0, 0.3),
    0 10px 25px rgba(59, 130, 246, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.highlightIcon {
  width: 55px;
  height: 55px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.4rem;
  margin: 0 auto 18px;
  box-shadow:
    0 10px 25px rgba(59, 130, 246, 0.4),
    0 4px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.highlightCard:hover .highlightIcon {
  transform: scale(1.1) rotate(5deg);
  box-shadow:
    0 15px 35px rgba(59, 130, 246, 0.6),
    0 6px 15px rgba(0, 0, 0, 0.3);
}

.highlightContent {
  position: relative;
  z-index: 2;
}

.highlightNumber {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
  margin-bottom: 8px;
}

.highlightLabel {
  font-size: 1rem;
  color: white;
  font-weight: 600;
  margin-bottom: 5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.highlightDescription {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.highlightGlow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.highlightCard:hover .highlightGlow {
  opacity: 1;
}



/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}



@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-30px) translateX(15px);
    opacity: 1;
  }
}

@keyframes shapeFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes waveMove {
  0%, 100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  33% {
    transform: translateX(40px) translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateX(-30px) translateY(15px) rotate(240deg);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px);
    opacity: 1;
  }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
  50% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4), 0 0 20px rgba(59, 130, 246, 0.3);
  }
}

@keyframes underlineGlow {
  0%, 100% {
    opacity: 0.7;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.05);
  }
}

/* Responsive Design */
@media (max-width: 1920px) {
  .servicesHero {
    height: 100vh;
    padding-top: 0; /* Removed padding to move content upwards */
    margin-top: -10px; /* Move content upwards by 10px */
    padding-bottom: 20px;
  }
}

@media (max-width: 1440px) {
  .servicesHero {
    height: 100vh;
    padding-top: 0; /* Removed padding to move content upwards */
    margin-top: -10px; /* Move content upwards by 10px */
    padding-bottom: 20px;
  }
}

@media (max-width: 1200px) {
  .servicesHero {
    height: 100vh;
    padding-top: 0; /* Removed padding to move content upwards */
    margin-top: -10px; /* Move content upwards by 10px */
    padding-bottom: 20px;
  }
}

@media (max-width: 1024px) {
  .servicesHero {
    height: 100vh;
    padding-top: 0; /* Removed padding to move content upwards */
    margin-top: -10px; /* Move content upwards by 10px */
    padding-bottom: 20px;
  }

  .heroGrid {
    gap: 60px;
  }

  .heroTitle {
    font-size: 3.5rem;
  }

  .heroDescription {
    font-size: 1.2rem;
  }

  .highlightCard {
    padding: 20px 15px;
  }



  .primaryButton,
  .secondaryButton {
    padding: 18px 35px;
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .servicesHero {
    height: auto;
    min-height: 100vh;
    padding-top: 0; /* Removed padding to move content upwards */
    margin-top: -10px; /* Move content upwards by 10px */
    padding-bottom: 20px;
  }

  .container {
    padding: 0 15px;
  }

  .heroGrid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .leftColumn,
  .rightColumn {
    padding: 0;
  }

  .heroTitle {
    font-size: 2.8rem;
    line-height: 1.2;
    text-align: center;
  }

  .heroDescription {
    font-size: 1.1rem;
    margin-bottom: 35px;
    text-align: center;
  }

  .heroActions {
    justify-content: center;
    gap: 15px;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .highlightsSection {
    margin-bottom: 30px;
  }

  .highlightsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .highlightCard {
    padding: 20px 15px;
  }

  .highlightIcon {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
    margin-bottom: 15px;
  }

  .highlightNumber {
    font-size: 1.8rem;
  }





  /* Simplify background effects on mobile */
  .geometricShapes,
  .floatingIcons,
  .gradientWaves {
    display: none;
  }

  .particleSystem {
    opacity: 0.3;
  }
}

@media (max-width: 480px) {
  .servicesHero {
    height: auto;
    min-height: 100vh;
    padding-top: 0; /* Removed padding to move content upwards */
    margin-top: -10px; /* Move content upwards by 10px */
    padding-bottom: 15px;
  }

  .heroGrid {
    gap: 30px;
  }

  .heroTitle {
    font-size: 2.2rem;
    line-height: 1.3;
  }

  .heroDescription {
    font-size: 1rem;
    margin-bottom: 25px;
  }

  .heroActions {
    flex-direction: column;
    gap: 12px;
  }

  .highlightsSection {
    margin-bottom: 25px;
  }

  .highlightsGrid {
    gap: 12px;
  }

  .highlightCard {
    padding: 16px 12px;
  }

  .highlightIcon {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
    margin-bottom: 12px;
  }

  .highlightNumber {
    font-size: 1.6rem;
  }

  .highlightLabel {
    font-size: 0.85rem;
  }

  .highlightDescription {
    font-size: 0.75rem;
  }





  .primaryButton,
  .secondaryButton {
    padding: 15px 25px;
    font-size: 0.9rem;
  }
}
