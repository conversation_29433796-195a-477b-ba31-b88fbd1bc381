/* Partner Australian Universities Section */
.partnerUniversities {
  padding: 5rem 0;
  background: linear-gradient(135deg, #0c1426 0%, #002868 50%, #1e3a8a 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

/* Background Effects */
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.floatingElement1,
.floatingElement2 {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff0000, #cc0000);
  opacity: 0.03;
  animation: float 10s ease-in-out infinite;
}

.floatingElement1 {
  width: 300px;
  height: 300px;
  top: 15%;
  right: 10%;
  animation-delay: 0s;
}

.floatingElement2 {
  width: 200px;
  height: 200px;
  bottom: 25%;
  left: 5%;
  animation-delay: 5s;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
}

/* Main Content */
.mainContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
}

@media (max-width: 968px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

.contentLeft {
  padding-right: 2rem;
}

@media (max-width: 968px) {
  .contentLeft {
    padding-right: 0;
  }
}

.sectionTitle {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.3rem;
  background: linear-gradient(135deg, #ff0000 0%, #ff4444 50%, #ff7777 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 2rem;
  font-weight: 600;
}

.description {
  margin-bottom: 2rem;
}

.description p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.brandSection {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.brandName {
  font-size: 1.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
}

/* Universities List */
.universitiesList {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 600px;
}

.listTitle {
  font-size: 1.3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 2rem;
  line-height: 1.4;
}

.universitiesGrid {
  display: grid;
  gap: 1rem;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.universitiesGrid::-webkit-scrollbar {
  width: 6px;
}

.universitiesGrid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.universitiesGrid::-webkit-scrollbar-thumb {
  background: rgba(255, 0, 0, 0.5);
  border-radius: 3px;
}

.universityItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  font-size: 1rem;
  color: #e2e8f0;
  transition: color 0.3s ease;
}

.universityItem:hover {
  color: #ff0000;
}

.universityItem i {
  color: #ff0000;
  font-size: 0.9rem;
  flex-shrink: 0;
}

/* Stats Section */
.statsSection {
  text-align: center;
  margin-bottom: 4rem;
}

.statCard {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ff0000 0%, #ff4444 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 1.1rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Features Section */
.featuresSection {
  text-align: center;
  margin-bottom: 5rem;
}

.featuresTitle {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.featuresSubtitle {
  font-size: 1.2rem;
  color: #cbd5e1;
  margin-bottom: 3rem;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.featureCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  border-color: #ff0000;
}

.featureIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #ff0000, #cc0000);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
}

.featureTitle {
  font-size: 1.3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.75rem;
}

.featureDescription {
  color: #cbd5e1;
  line-height: 1.6;
}

/* CTA Section */
.ctaSection {
  margin-top: 2rem;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(45deg, #ff0000, #cc0000);
  color: white;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 0, 0, 0.4);
  background: linear-gradient(45deg, #cc0000, #990000);
}

/* Final CTA Section */
.finalCtaSection {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  padding: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ctaContent {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-bottom: 3rem;
}

@media (max-width: 968px) {
  .ctaContent {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

.textContent {
  padding-right: 2rem;
}

@media (max-width: 968px) {
  .textContent {
    padding-right: 0;
  }
}

.ctaTitle {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.ctaDescription {
  font-size: 1.2rem;
  color: #cbd5e1;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.featuresList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (max-width: 968px) {
  .featuresList {
    justify-content: center;
    align-items: flex-start;
    max-width: 300px;
    margin: 0 auto;
  }
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  color: white;
  font-weight: 600;
}

.featureItem i {
  color: #ff0000;
  font-size: 1rem;
  flex-shrink: 0;
}

/* Stats Content */
.statsContent {
  display: flex;
  justify-content: center;
}

.finalStatCard {
  background: linear-gradient(45deg, #ff0000, #cc0000);
  color: white;
  padding: 2.5rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(255, 0, 0, 0.3);
  transform: rotate(-2deg);
  transition: transform 0.3s ease;
}

.finalStatCard:hover {
  transform: rotate(0deg) scale(1.05);
}

.finalStatNumber {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.finalStatLabel {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.finalStatDescription {
  font-size: 0.95rem;
  opacity: 0.9;
  line-height: 1.4;
}

/* Final CTA Actions */
.finalCtaActions {
  text-align: center;
}

.primaryButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #ff0000, #cc0000);
  color: white;
  padding: 1.25rem 3rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(255, 0, 0, 0.4);
  margin-bottom: 1rem;
}

.primaryButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(255, 0, 0, 0.5);
  background: linear-gradient(45deg, #cc0000, #990000);
}

.disclaimer {
  font-size: 0.9rem;
  color: #94a3b8;
  font-style: italic;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .partnerUniversities {
    padding: 3rem 0;
  }

  .mainContent {
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .universitiesList {
    padding: 2rem;
    min-height: 450px;
  }

  .universitiesGrid {
    max-height: 350px;
  }

  .statCard {
    padding: 1.5rem 2rem;
  }

  .statNumber {
    font-size: 2.5rem;
  }

  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .featureCard {
    padding: 1.5rem;
  }

  .finalCtaSection {
    padding: 2rem;
  }

  .finalStatCard {
    padding: 2rem;
    transform: rotate(0deg);
  }

  .finalStatNumber {
    font-size: 2.5rem;
  }

  .primaryButton {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
    max-width: 350px;
  }
}
