/* Partner New Zealand Universities Section */
.partnerUniversities {
  position: relative;
  padding: 5rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  color: white;
  overflow: hidden;
}

/* Background Effects */
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.floatingElement1,
.floatingElement2 {
  position: absolute;
  border-radius: 50%;
  animation: float 10s ease-in-out infinite;
}

.floatingElement1 {
  background: rgba(0, 34, 68, 0.15);
  width: 250px;
  height: 250px;
  top: 15%;
  right: 10%;
  animation-delay: 0s;
}

.floatingElement2 {
  background: rgba(220, 38, 38, 0.12);
  width: 180px;
  height: 180px;
  bottom: 20%;
  left: 15%;
  animation-delay: 5s;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 800;
  color: #f1f5f9;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.sectionSubtitle {
  font-size: 1.2rem;
  color: #cbd5e1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Main Content */
.mainContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 4rem;
  align-items: start;
}

/* Content Left */
.contentLeft {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.headerSection {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 34, 68, 0.3);
  border-radius: 20px;
  padding: 2rem;
}

.mainTitle {
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  font-weight: 800;
  color: white;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.missionText {
  font-size: 1rem;
  line-height: 1.6;
  color: #e2e8f0;
  margin-bottom: 1.5rem;
}

/* Stats Section */
.statsSection {
  display: flex;
  gap: 2rem;
}

.statItem {
  text-align: center;
}

.statNumber {
  display: block;
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, #dc2626, #991b1b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.statLabel {
  display: block;
  font-size: 0.9rem;
  color: #cbd5e1;
  margin-top: 0.5rem;
  max-width: 200px;
  line-height: 1.3;
}

/* Brand Section */
.brandSection {
  background: rgba(0, 34, 68, 0.3);
  border: 1px solid rgba(0, 34, 68, 0.4);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
}

.brandName {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
}

/* Content Right */
.contentRight {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 34, 68, 0.3);
  border-radius: 20px;
  padding: 2rem;
}

.listTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

/* Universities Grid */
.universitiesGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.universityItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 34, 68, 0.2);
}

.universityItem:hover {
  background: rgba(0, 34, 68, 0.2);
  transform: translateX(5px);
  border-color: rgba(220, 38, 38, 0.3);
}

.universityItem i {
  color: #dc2626;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.universityItem span {
  color: #e2e8f0;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Features Section */
.featuresSection {
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 25px;
  padding: 3rem 2rem;
  border: 1px solid rgba(0, 34, 68, 0.2);
}

.featuresTitle {
  font-size: clamp(1.8rem, 3vw, 2.2rem);
  font-weight: 800;
  color: white;
  margin-bottom: 0.5rem;
}

.featuresSubtitle {
  font-size: 1.1rem;
  color: #cbd5e1;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Features Grid */
.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.featureCard {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 34, 68, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-5px);
  background: rgba(0, 34, 68, 0.2);
  border-color: rgba(220, 38, 38, 0.3);
}

.featureIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #dc2626, #991b1b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.featureIcon i {
  font-size: 1.5rem;
  color: white;
}

.featureTitle {
  font-size: 1.1rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.featureDescription {
  font-size: 0.9rem;
  color: #cbd5e1;
  line-height: 1.5;
}

/* CTA Section */
.ctaSection {
  text-align: center;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(45deg, #dc2626, #991b1b);
  color: white;
  padding: 1rem 2.5rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(220, 38, 38, 0.4);
}

.ctaButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(220, 38, 38, 0.6);
}

/* Final CTA Section */
.finalCtaSection {
  margin-top: 4rem;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 25px;
  padding: 3rem 2rem;
  border: 1px solid rgba(0, 34, 68, 0.3);
  text-align: center;
}

.finalCtaContent {
  max-width: 800px;
  margin: 0 auto;
}

.finalCtaTitle {
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  font-weight: 800;
  color: #f1f5f9;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.finalCtaDescription {
  font-size: 1.1rem;
  color: #cbd5e1;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.finalCtaFeatures {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2.5rem;
  flex-wrap: wrap;
}

.finalCtaFeature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e2e8f0;
  font-weight: 500;
}

.finalCtaFeature i {
  color: #dc2626;
  font-size: 0.9rem;
}

.finalCtaButtons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.finalCtaButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(45deg, #dc2626, #991b1b);
  color: white;
  padding: 1.2rem 3rem;
  border-radius: 35px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 6px 25px rgba(220, 38, 38, 0.4);
}

.finalCtaButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 35px rgba(220, 38, 38, 0.6);
}

.finalCtaNote {
  font-size: 0.9rem;
  color: #94a3b8;
  margin: 0;
  font-style: italic;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .partnerUniversities {
    padding: 3rem 0;
  }

  .sectionHeader {
    margin-bottom: 3rem;
  }

  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .statsSection {
    justify-content: center;
    gap: 1.5rem;
    flex-direction: column;
  }

  .featuresSection {
    padding: 2rem 1rem;
  }

  .finalCtaSection {
    margin-top: 3rem;
    padding: 2rem 1rem;
  }

  .finalCtaFeatures {
    flex-direction: column;
    gap: 1rem;
  }

  .finalCtaButton {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}
