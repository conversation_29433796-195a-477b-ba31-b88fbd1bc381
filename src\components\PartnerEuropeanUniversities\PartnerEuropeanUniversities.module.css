/* Partner European Universities Section */
.partnerUniversities {
  padding: 5rem 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  color: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Main Content */
.mainContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
}

@media (max-width: 968px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

.contentLeft {
  padding-right: 2rem;
}

@media (max-width: 968px) {
  .contentLeft {
    padding-right: 0;
  }
}

.sectionTitle {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  color: white;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.3rem;
  color: #60a5fa;
  margin-bottom: 2rem;
  font-weight: 600;
}

.description {
  margin-bottom: 2rem;
}

.description p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.brandSection {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.brandName {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
  text-align: center;
}

/* Universities List */
.universitiesList {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 600px;
}

.listTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 2rem;
  line-height: 1.4;
}

.universitiesGrid {
  display: grid;
  gap: 1rem;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.universitiesGrid::-webkit-scrollbar {
  width: 6px;
}

.universitiesGrid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.universitiesGrid::-webkit-scrollbar-thumb {
  background: rgba(96, 165, 250, 0.5);
  border-radius: 3px;
}

.universityItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  font-size: 1rem;
  color: #e2e8f0;
  transition: color 0.3s ease;
}

.universityItem:hover {
  color: #60a5fa;
}

.universityItem i {
  color: #60a5fa;
  font-size: 0.9rem;
  flex-shrink: 0;
}

/* Stats Section */
.statsSection {
  text-align: center;
  margin-bottom: 4rem;
}

.statCard {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  color: #60a5fa;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 1.1rem;
  color: white;
  font-weight: 600;
}

/* Features Section */
.featuresSection {
  text-align: center;
}

.featuresTitle {
  font-size: 2.5rem;
  font-weight: 800;
  color: white;
  margin-bottom: 1rem;
}

.featuresSubtitle {
  font-size: 1.2rem;
  color: #cbd5e1;
  margin-bottom: 3rem;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.featureCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  border-color: #60a5fa;
}

.featureIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
}

.featureTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.75rem;
}

.featureDescription {
  color: #cbd5e1;
  line-height: 1.6;
}

/* CTA Section */
.ctaSection {
  margin-top: 2rem;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  color: white;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  background: linear-gradient(45deg, #2563eb, #1d4ed8);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .partnerUniversities {
    padding: 3rem 0;
  }
  
  .mainContent {
    gap: 2rem;
    margin-bottom: 3rem;
  }
  
  .universitiesList {
    padding: 2rem;
    min-height: 450px;
  }

  .universitiesGrid {
    max-height: 350px;
  }
  
  .statCard {
    padding: 1.5rem 2rem;
  }
  
  .statNumber {
    font-size: 2.5rem;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .featureCard {
    padding: 1.5rem;
  }
}
