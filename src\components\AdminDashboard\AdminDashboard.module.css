/* Admin Dashboard Component Styles */

.dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  position: relative;
}

.dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
  pointer-events: none;
  animation: backgroundPulse 25s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Header */
.header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  padding: 20px 0;
  position: relative;
  z-index: 10;
}

.headerContent {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.headerLeft {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.title {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #3b82f6 50%, #60a5fa 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  line-height: 1.1;
}

.subtitle {
  color: #94a3b8;
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 20px;
}

.userInfo {
  color: #e2e8f0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logoutButton {
  padding: 12px 20px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logoutButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Loading */
.loadingContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  color: #e2e8f0;
  text-align: center;
}

.loadingSpinner {
  font-size: 3rem;
  color: #3b82f6;
  margin-bottom: 20px;
}

/* Error Message */
.errorMessage {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.1));
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  padding: 16px 20px;
  margin: 20px;
  color: #fca5a5;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 5;
}

.closeError {
  background: none;
  border: none;
  color: #fca5a5;
  cursor: pointer;
  margin-left: auto;
  padding: 5px;
  border-radius: 5px;
  transition: background-color 0.2s ease;
}

.closeError:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Content */
.content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 2;
}

/* Stats */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.statCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.statIcon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.statInfo h3 {
  font-size: 2.5rem;
  font-weight: 900;
  color: #ffffff;
  margin: 0 0 5px 0;
  line-height: 1;
}

.statInfo p {
  color: #94a3b8;
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

/* Table Container */
.tableContainer {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  padding: 30px;
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.3),
    0 10px 30px rgba(59, 130, 246, 0.1);
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.tableHeader h2 {
  font-size: 1.8rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0;
}

.refreshButton {
  padding: 10px 16px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #94a3b8;
}

.emptyState i {
  font-size: 4rem;
  color: #475569;
  margin-bottom: 20px;
}

.emptyState h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e2e8f0;
  margin-bottom: 10px;
}

.emptyState p {
  font-size: 1rem;
  line-height: 1.6;
}

/* Table */
.table {
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tableHead {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tableBody {
  background: rgba(255, 255, 255, 0.02);
}

.tableRow {
  display: grid;
  grid-template-columns: 1fr 1.5fr 1.5fr 1fr 0.8fr 1.2fr;
  gap: 20px;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: background-color 0.2s ease;
}

.tableBody .tableRow:hover {
  background: rgba(255, 255, 255, 0.05);
}

.tableCell {
  display: flex;
  align-items: center;
  color: #e2e8f0;
  font-size: 0.95rem;
  line-height: 1.4;
}

.tableHead .tableCell {
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 1px;
}

/* Status Badges */
.statusBadge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusNew {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.statusRead {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.statusReplied {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.viewButton,
.deleteButton {
  padding: 8px 10px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.viewButton {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.viewButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.deleteButton {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.deleteButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.statusSelect {
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.statusSelect:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.15);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  padding: 0;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow:
    0 20px 50px rgba(0, 0, 0, 0.5),
    0 10px 30px rgba(59, 130, 246, 0.2);
}

.modalHeader {
  padding: 25px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05));
}

.modalHeader h3 {
  font-size: 1.5rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0;
}

.closeModal {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 1.2rem;
}

.closeModal:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.modalBody {
  padding: 30px;
  overflow-y: auto;
  max-height: calc(80vh - 100px);
}

.detailRow {
  margin-bottom: 20px;
  color: #e2e8f0;
  font-size: 1rem;
  line-height: 1.6;
}

.detailRow strong {
  color: #ffffff;
  font-weight: 600;
  margin-right: 10px;
}

.messageSection {
  margin-top: 25px;
}

.messageContent {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-top: 10px;
  color: #e2e8f0;
  font-size: 1rem;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .tableRow {
    grid-template-columns: 1fr 1.5fr 1.5fr 1fr 0.8fr 1fr;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .headerContent {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .headerRight {
    flex-direction: column;
    gap: 15px;
  }

  .title {
    font-size: 2rem;
  }

  .stats {
    grid-template-columns: 1fr;
  }

  .tableContainer {
    padding: 20px;
    overflow-x: auto;
  }

  .table {
    min-width: 800px;
  }

  .modalContent {
    margin: 10px;
    max-height: 90vh;
  }

  .modalHeader {
    padding: 20px;
  }

  .modalBody {
    padding: 20px;
  }
}
