rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Contact submissions collection
    match /contact_submissions/{document} {
      // Allow anyone to create contact submissions (for the contact form)
      allow create: if true;
      
      // Only authenticated users can read, update, and delete submissions
      allow read, update, delete: if request.auth != null;
    }
    
    // Deny all other access by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

/*
IMPORTANT: You need to apply these rules in the Firebase Console:

1. Go to https://console.firebase.google.com/
2. Select your project: learn-education-834bb
3. Navigate to Firestore Database
4. Click on "Rules" tab
5. Replace the existing rules with the content above
6. Click "Publish"

These rules ensure:
- Anyone can submit contact forms (create operation)
- Only authenticated admin users can view, update, and delete submissions
- All other operations are denied by default for security
*/
