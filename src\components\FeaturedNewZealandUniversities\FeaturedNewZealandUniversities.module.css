/* Featured New Zealand Universities Section */
.featuredUniversities {
  position: relative;
  padding: 5rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  overflow: hidden;
}

/* Background Effects */
.backgroundEffects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.floatingElement1,
.floatingElement2,
.floatingElement3 {
  position: absolute;
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
}

.floatingElement1 {
  background: rgba(0, 34, 68, 0.1);
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floatingElement2 {
  background: rgba(220, 38, 38, 0.08);
  width: 150px;
  height: 150px;
  top: 70%;
  right: 15%;
  animation-delay: 3s;
}

.floatingElement3 {
  background: rgba(0, 34, 68, 0.06);
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 6s;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  color: #f1f5f9;
  margin-bottom: 1rem;
}

.sectionDescription {
  font-size: 1.1rem;
  color: #cbd5e1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Universities Grid */
.universitiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .universitiesGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200px) {
  .universitiesGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* University Card */
.universityCard {
  background: linear-gradient(145deg, #1e293b, #334155);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 34, 68, 0.3);
}

.universityCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(220, 38, 38, 0.3);
}

/* Card Image */
.cardImage {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.cardImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.universityCard:hover .cardImage img {
  transform: scale(1.05);
}

.badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: linear-gradient(45deg, #dc2626, #991b1b);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
}

/* Card Content */
.cardContent {
  padding: 1.5rem;
}

.universityName {
  font-size: 1.3rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 1rem;
  line-height: 1.3;
}

/* University Details */
.universityDetails {
  margin-bottom: 1.5rem;
}

.detailItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #cbd5e1;
}

.detailItem i {
  color: #60a5fa;
  width: 16px;
  text-align: center;
}

/* Card Actions */
.cardActions {
  display: flex;
  justify-content: center;
}

.learnMoreBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(45deg, #1e40af, #1e3a8a);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.learnMoreBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(30, 64, 175, 0.4);
}

/* View All Section */
.viewAllSection {
  text-align: center;
}

.viewAllBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(45deg, #dc2626, #991b1b);
  color: white;
  padding: 1rem 2rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(220, 38, 38, 0.4);
}

.viewAllBtn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.6);
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* Mobile Responsive */
@media (max-width: 767px) {
  .featuredUniversities {
    padding: 3rem 0;
  }

  .universitiesGrid {
    grid-template-columns: 1fr !important;
    gap: 1.5rem;
    max-width: 100%;
  }

  .sectionHeader {
    margin-bottom: 2rem;
  }

  .universityCard {
    max-width: 400px;
    margin: 0 auto;
  }
}
