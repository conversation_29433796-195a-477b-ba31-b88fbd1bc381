/* Featured European Universities Section */
.featuredUniversities {
  padding: 5rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  position: relative;
  overflow: hidden;
}

.featuredUniversities::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23475569" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.1;
  z-index: 1;
}

.featuredUniversities::after {
  content: '';
  position: absolute;
  top: 10%;
  right: 5%;
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  border-radius: 50%;
  opacity: 0.05;
  animation: float 8s ease-in-out infinite;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  color: white;
  margin-bottom: 1rem;
}

.sectionDescription {
  font-size: 1.2rem;
  color: #cbd5e1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Universities Grid */
.universitiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  justify-items: center;
}

@media (max-width: 768px) {
  .universitiesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* University Card */
.universityCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  max-width: 400px;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.universityCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  background: rgba(255, 255, 255, 0.15);
  border-color: #60a5fa;
}

/* Card Image */
.cardImage {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.cardImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.universityCard:hover .cardImage img {
  transform: scale(1.05);
}

.badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Card Content */
.cardContent {
  padding: 1.5rem;
}

.universityName {
  font-size: 1.4rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.universityDetails {
  margin-bottom: 1.5rem;
}

.detailItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  color: #cbd5e1;
}

.detailItem i {
  width: 16px;
  color: #60a5fa;
  font-size: 0.9rem;
}

.detailItem:last-child {
  margin-bottom: 0;
}

/* Learn More Button */
.learnMoreBtn {
  display: inline-block;
  width: 100%;
  background: linear-gradient(45deg, #3b82f6, #2563eb);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.learnMoreBtn:hover {
  background: linear-gradient(45deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .featuredUniversities {
    padding: 3rem 0;
  }
  
  .sectionHeader {
    margin-bottom: 2.5rem;
  }
  
  .cardImage {
    height: 200px;
  }
  
  .cardContent {
    padding: 1rem;
  }
  
  .universityName {
    font-size: 1.2rem;
  }
  
  .detailItem {
    font-size: 0.85rem;
  }
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
