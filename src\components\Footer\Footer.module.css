/* Footer Component Styles */
.footer {
  background: linear-gradient(135deg, #1e3a8a 0%, #2b70fa 100%);
  color: white;
  margin-top: 0;
}

.footerContainer {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .footerContainer {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .footerContainer {
    padding: 0 2rem;
  }
}

.footerContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2.5rem;
  padding: 3rem 0;
}

@media (min-width: 768px) {
  .footerContent {
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
  }
}

@media (min-width: 1024px) {
  .footerContent {
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 4rem;
    padding: 4rem 0;
  }
}

/* Logo Section */
.logoSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.logoContainer {
  flex-shrink: 0;
}

.logoLink {
  display: inline-block;
  transition: transform 0.3s ease;
}

.logoLink:hover {
  transform: scale(1.05);
}

.logo {
  height: auto;
  width: auto;
  max-width: 200px;
  filter: brightness(0) invert(1);
}

.visionText {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

@media (min-width: 1024px) {
  .visionText {
    font-size: 1.1rem;
  }
}

/* Social Media */
.socialMedia {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 50%;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.socialLink i {
  font-style: normal !important;
}

.socialLink:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Platform-specific hover colors */
.socialLink:hover .fa-youtube {
  color: #ff0000;
}

.socialLink:hover .fa-facebook {
  color: #1877f2;
}

.socialLink:hover .fa-x-twitter {
  color: #000000;
}

.socialLink:hover .fa-linkedin {
  color: #0a66c2;
}

@media (min-width: 768px) {
  .socialMedia {
    gap: 1.25rem;
  }

  .socialLink {
    width: 3rem;
    height: 3rem;
    font-size: 1.4rem;
  }
}

/* Link Sections */
.linkSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: white;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 0.5rem;
}

@media (min-width: 1024px) {
  .sectionTitle {
    font-size: 1.375rem;
  }
}

.linkList {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.linkItem {
  margin: 0;
}

.footerLink {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
}

.footerLink:hover {
  color: white;
  transform: translateX(5px);
}

.footerLink::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 2px;
  background-color: #60a5fa;
  transition: width 0.3s ease;
}

.footerLink:hover::before {
  width: 10px;
}

/* Contact Section */
.contactSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contactBlock {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contactSubtitle {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #60a5fa;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contactSubtitle i {
  font-size: 1.1rem;
}

.address {
  font-style: normal;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  margin: 0;
  font-size: 0.9rem;
}

.contactDetails {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 0.5rem;
}

.contactItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.contactItem i {
  font-size: 1.1rem;
  color: #60a5fa;
  width: 20px;
  text-align: center;
}

.contactLink {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.contactLink:hover {
  color: #60a5fa;
}

/* Footer Bottom */
.footerBottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem 0;
}

.footerBottomContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
}

@media (min-width: 768px) {
  .footerBottomContent {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

.copyright {
  margin: 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.designCredit {
  margin: 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.designerName {
  color: #60a5fa;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.designerName:hover {
  color: white;
  text-decoration: underline;
}

/* Scroll to Top Button */
.scrollToTop {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #2b70fa 0%, #1e3a8a 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 4px 12px rgba(43, 112, 250, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  animation: fadeInUp 0.3s ease;
}

.scrollToTop:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(43, 112, 250, 0.4);
  background: linear-gradient(135deg, #1e3a8a 0%, #2b70fa 100%);
}

.scrollToTop:active {
  transform: translateY(0);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .scrollToTop {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
}
